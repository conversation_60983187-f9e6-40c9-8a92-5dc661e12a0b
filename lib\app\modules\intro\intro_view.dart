import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/animated_background.dart';
import '../../core/widgets/glass_card.dart';
import '../../core/widgets/gradient_button.dart';
import '../../core/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';
import 'intro_controller.dart';

class IntroView extends GetView<IntroController> {
  const IntroView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Skip Button
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: controller.skipIntro,
                      child: Text(
                        'Skip',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ],
                ),
              ),

              // Page View
              Expanded(
                child: PageView.builder(
                  controller: controller.pageController,
                  itemCount: controller.introItems.length,
                  itemBuilder: (context, index) {
                    final item = controller.introItems[index];
                    return Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Icon with Animation
                          TweenAnimationBuilder<double>(
                            duration: AppConstants.longAnimationDuration,
                            tween: Tween(begin: 0.0, end: 1.0),
                            builder: (context, value, child) {
                              return Transform.scale(
                                scale: value,
                                child: Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      colors: item.gradient,
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: item.gradient.first
                                            .withValues(alpha: 0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 8),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    item.icon,
                                    size: 60,
                                    color: Colors.white,
                                  ),
                                ),
                              );
                            },
                          ),

                          const SizedBox(height: 48),

                          // Content Card
                          FlatCard(
                            padding: const EdgeInsets.all(32),
                            child: Column(
                              children: [
                                Text(
                                  item.title,
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textPrimary,
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  item.description,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                        color: AppColors.textSecondary,
                                        height: 1.5,
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // Bottom Navigation
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Page Indicators
                    Obx(() => Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            controller.introItems.length,
                            (index) => AnimatedContainer(
                              duration: AppConstants.animationDuration,
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              width: controller.currentPage.value == index
                                  ? 24
                                  : 8,
                              height: 8,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: controller.currentPage.value == index
                                    ? AppColors.primaryPastel
                                    : AppColors.textLight,
                              ),
                            ),
                          ),
                        )),

                    const SizedBox(height: 32),

                    // Navigation Buttons
                    Row(
                      children: [
                        // Previous Button
                        Obx(() => controller.currentPage.value > 0
                            ? Expanded(
                                child: OutlinedButton(
                                  onPressed: controller.previousPage,
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    side: const BorderSide(
                                        color: AppColors.primaryPastel),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius),
                                    ),
                                  ),
                                  child: Text(
                                    'Previous',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          color: AppColors.primaryPastel,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink()),

                        if (controller.currentPage.value > 0)
                          const SizedBox(width: 16),

                        // Next/Get Started Button
                        Expanded(
                          flex: controller.currentPage.value > 0 ? 1 : 2,
                          child: Obx(() => GradientButton(
                                text: controller.isLastPage.value
                                    ? 'Get Started'
                                    : 'Next',
                                onPressed: controller.nextPage,
                                gradient: AppColors.primaryGradient,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
