import 'package:get/get.dart';
import '../services/storage_service.dart';
import '../services/network_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize core services (async eager init so Get.find works after boot)
    Get.putAsync<StorageService>(() => StorageService().init(),
        permanent: true);
    Get.putAsync<NetworkService>(() => NetworkService().init(),
        permanent: true);
  }
}
