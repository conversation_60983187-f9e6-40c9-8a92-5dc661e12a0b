import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Enhanced bottom navigation with advanced animations and micro-interactions
class EnhancedBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<EnhancedBottomNavigationItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double height;
  final bool showLabels;
  final EdgeInsets? padding;
  final double borderRadius;
  final bool enableHapticFeedback;
  final bool enableRippleEffect;
  final bool enableFloatingIndicator;

  const EnhancedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.height = 72,
    this.showLabels = true,
    this.padding,
    this.borderRadius = 16,
    this.enableHapticFeedback = true,
    this.enableRippleEffect = true,
    this.enableFloatingIndicator = true,
  });

  @override
  State<EnhancedBottomNavigation> createState() =>
      _EnhancedBottomNavigationState();
}

class _EnhancedBottomNavigationState extends State<EnhancedBottomNavigation>
    with TickerProviderStateMixin {
  late final AnimationController _indicatorController;
  late final AnimationController _iconController;
  late final AnimationController _rippleController;
  late final AnimationController _floatingController;

  late final Animation<double> _indicatorSlideAnimation;
  late final Animation<double> _iconBounceAnimation;
  late final Animation<double> _rippleAnimation;
  late final Animation<double> _floatingAnimation;

  int _previousIndex = 0;

  @override
  void initState() {
    super.initState();

    _indicatorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _iconController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _rippleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _floatingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _indicatorSlideAnimation = CurvedAnimation(
      parent: _indicatorController,
      curve: Curves.easeInOutCubic,
    );

    _iconBounceAnimation = CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    );

    _rippleAnimation = CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    );

    _floatingAnimation = CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    );

    // Start floating animation
    if (widget.enableFloatingIndicator) {
      _floatingController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(covariant EnhancedBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _previousIndex = oldWidget.currentIndex;
      _animateTransition();
    }
  }

  void _animateTransition() {
    // Reset and start animations
    _indicatorController.forward(from: 0);
    _iconController.forward(from: 0);

    if (widget.enableRippleEffect) {
      _rippleController.forward(from: 0);
    }

    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  @override
  void dispose() {
    _indicatorController.dispose();
    _iconController.dispose();
    _rippleController.dispose();
    _floatingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final safeBottom = MediaQuery.of(context).padding.bottom;

    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.surface;
    final selectedColor = widget.selectedItemColor ?? theme.colorScheme.primary;
    final unselectedColor = widget.unselectedItemColor ??
        theme.colorScheme.onSurface.withValues(alpha: 0.6);

    return Container(
      height: widget.height + safeBottom,
      padding: widget.padding ?? EdgeInsets.fromLTRB(16, 8, 16, safeBottom),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(widget.borderRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: _EnhancedNavigationContent(
        height: widget.height,
        items: widget.items,
        currentIndex: widget.currentIndex,
        previousIndex: _previousIndex,
        onTap: widget.onTap,
        selectedColor: selectedColor,
        unselectedColor: unselectedColor,
        showLabels: widget.showLabels,
        indicatorAnimation: _indicatorSlideAnimation,
        iconBounceAnimation: _iconBounceAnimation,
        rippleAnimation: _rippleAnimation,
        floatingAnimation: _floatingAnimation,
        enableRippleEffect: widget.enableRippleEffect,
        enableFloatingIndicator: widget.enableFloatingIndicator,
      ),
    );
  }
}

class _EnhancedNavigationContent extends StatelessWidget {
  final double height;
  final List<EnhancedBottomNavigationItem> items;
  final int currentIndex;
  final int previousIndex;
  final Function(int) onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final bool showLabels;
  final Animation<double> indicatorAnimation;
  final Animation<double> iconBounceAnimation;
  final Animation<double> rippleAnimation;
  final Animation<double> floatingAnimation;
  final bool enableRippleEffect;
  final bool enableFloatingIndicator;

  const _EnhancedNavigationContent({
    required this.height,
    required this.items,
    required this.currentIndex,
    required this.previousIndex,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
    required this.showLabels,
    required this.indicatorAnimation,
    required this.iconBounceAnimation,
    required this.rippleAnimation,
    required this.floatingAnimation,
    required this.enableRippleEffect,
    required this.enableFloatingIndicator,
  });

  @override
  Widget build(BuildContext context) {
    final itemWidth = MediaQuery.of(context).size.width / items.length;

    return SizedBox(
      height: height,
      child: Stack(
        children: [
          // Ripple effect
          if (enableRippleEffect)
            AnimatedBuilder(
              animation: rippleAnimation,
              builder: (context, _) {
                final rippleOffset =
                    (currentIndex * itemWidth) + (itemWidth / 2);
                final rippleRadius = rippleAnimation.value * 40;

                return Positioned(
                  left: rippleOffset - rippleRadius,
                  top: (height / 2) - rippleRadius,
                  child: Container(
                    width: rippleRadius * 2,
                    height: rippleRadius * 2,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: selectedColor.withValues(
                        alpha: 0.1 * (1 - rippleAnimation.value),
                      ),
                    ),
                  ),
                );
              },
            ),

          // Floating indicator
          AnimatedBuilder(
            animation:
                Listenable.merge([indicatorAnimation, floatingAnimation]),
            builder: (context, _) {
              final indicatorOffset =
                  (currentIndex * itemWidth) + (itemWidth / 2) - 28;
              final floatingOffset = enableFloatingIndicator
                  ? math.sin(floatingAnimation.value * 2 * math.pi) * 2
                  : 0.0;

              return Positioned(
                left: indicatorOffset,
                top: 6 + floatingOffset,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOutCubic,
                  width: 56,
                  height: 36,
                  decoration: BoxDecoration(
                    color: selectedColor.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(18),
                    boxShadow: [
                      BoxShadow(
                        color: selectedColor.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // Navigation items
          Row(
            children: List.generate(items.length, (index) {
              final isSelected = index == currentIndex;
              return Expanded(
                child: _EnhancedNavigationItem(
                  item: items[index],
                  isSelected: isSelected,
                  onTap: () => onTap(index),
                  selectedColor: selectedColor,
                  unselectedColor: unselectedColor,
                  showLabel: showLabels,
                  iconBounceAnimation: isSelected ? iconBounceAnimation : null,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _EnhancedNavigationItem extends StatefulWidget {
  final EnhancedBottomNavigationItem item;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final bool showLabel;
  final Animation<double>? iconBounceAnimation;

  const _EnhancedNavigationItem({
    required this.item,
    required this.isSelected,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
    required this.showLabel,
    this.iconBounceAnimation,
  });

  @override
  State<_EnhancedNavigationItem> createState() =>
      _EnhancedNavigationItemState();
}

class _EnhancedNavigationItemState extends State<_EnhancedNavigationItem>
    with SingleTickerProviderStateMixin {
  late final AnimationController _hoverController;
  late final Animation<double> _hoverAnimation;
  // Removed unused _isHovered field

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _hoverAnimation = CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(20),
          child: AnimatedBuilder(
            animation: _hoverAnimation,
            builder: (context, _) {
              return Transform.scale(
                scale: 1.0 + (_hoverAnimation.value * 0.05),
                child: Container(
                  height: 64,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Icon with bounce animation and badge
                      _buildAnimatedIcon(theme),

                      // Label with fade animation
                      if (widget.showLabel) ...[
                        const SizedBox(height: 4),
                        _buildAnimatedLabel(theme),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedIcon(ThemeData theme) {
    return AnimatedBuilder(
      animation:
          widget.iconBounceAnimation ?? const AlwaysStoppedAnimation(1.0),
      builder: (context, _) {
        final bounceScale =
            widget.isSelected && widget.iconBounceAnimation != null
                ? 1.0 + (widget.iconBounceAnimation!.value * 0.3)
                : 1.0;

        return Transform.scale(
          scale: bounceScale,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.all(8),
                child: Icon(
                  widget.isSelected
                      ? widget.item.selectedIcon ?? widget.item.icon
                      : widget.item.icon,
                  size: 24,
                  color: widget.isSelected
                      ? widget.selectedColor
                      : widget.unselectedColor,
                ),
              ),
              // Enhanced badge with pulse animation
              if (widget.item.badgeCount != null && widget.item.badgeCount! > 0)
                _buildPulseBadge(theme),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPulseBadge(ThemeData theme) {
    return Positioned(
      right: 4,
      top: 4,
      child: TweenAnimationBuilder<double>(
        tween: Tween(begin: 0.8, end: 1.2),
        duration: const Duration(milliseconds: 1000),
        builder: (context, scale, child) {
          return Transform.scale(
            scale: scale,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: theme.colorScheme.error,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.error.withValues(alpha: 0.4),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                widget.item.badgeCount! > 99
                    ? '99+'
                    : '${widget.item.badgeCount}',
                style: theme.textTheme.labelSmall!.copyWith(
                  color: theme.colorScheme.onError,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnimatedLabel(ThemeData theme) {
    return AnimatedDefaultTextStyle(
      duration: const Duration(milliseconds: 200),
      style: theme.textTheme.labelSmall!.copyWith(
        color:
            widget.isSelected ? widget.selectedColor : widget.unselectedColor,
        fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
      child: Text(
        widget.item.label,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

/// Enhanced navigation item data class
class EnhancedBottomNavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final int? badgeCount;

  const EnhancedBottomNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.badgeCount,
  });
}
